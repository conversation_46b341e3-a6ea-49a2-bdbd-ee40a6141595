<template>
    <div v-if="profile" key="profile-loaded">
        <div class="w-full px-4 py-2 border-b border-gray-200 dark:border-zinc-600 flex justify-end">
            <div class="w-28 flex items-center justify-end">
                <CorePrimaryButton @click="isEditing ? updateProfile() : toggleEdit()"
                    :start-icon="isEditing ? 'solar:list-check-bold' : 'line-md:edit'"
                    :text="isEditing ? 'Save' : 'Update'" />
            </div>
        </div>

        <div v-if="profile">
            <div class="w-full border-b border-dotted border-gray-200 dark:border-zinc-600 px-4 py-2 bg-gray-50 dark:bg-zinc-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-zinc-100">Name</h3>
                <div v-if="!isEditing">
                    <p class="text-base text-gray-600 dark:text-zinc-300">{{ profile.name }}</p>
                </div>
                <div v-else>
                    <FormKit type="text" prefixIcon="user" v-model="form.name" />
                </div>
            </div>

            <div class="w-full border-b border-dotted border-gray-200 dark:border-zinc-600 px-4 py-2 bg-white dark:bg-zinc-800">
                <h3 class="text-lg font-medium text-gray-900 dark:text-zinc-100">Tell us about yourself</h3>
                <div v-if="!isEditing">
                    <p v-html="profile.profile?.about"
                        class="text-base text-gray-600 dark:text-zinc-300 px-2 py-2 bmt-2">
                    </p>
                </div>
                <div v-else>
                    <FormKit type="textarea" v-model="form.about" />
                </div>
            </div>

            <div class="w-full border-b border-dotted border-gray-200 dark:border-zinc-600 px-4 py-2 bg-gray-50 dark:bg-zinc-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-zinc-100">Find me on</h3>
                <div v-if="!isEditing">
                    <p class="text-base text-gray-600 dark:text-zinc-300">{{ form.socialMedia.twitter }}</p>
                </div>
                <div class="flex flex-col items-center space-y-2.5" v-else>
                    <FormKit type="text" v-model="form.socialMedia.twitter" prefixIcon="twitter" />
                    <FormKit type="text" v-model="form.socialMedia.facebook" prefixIcon="facebook" />
                    <FormKit type="text" v-model="form.socialMedia.instagram" prefixIcon="instagram" />
                </div>
            </div>


            <div class="w-full border-b border-dotted border-gray-200 dark:border-zinc-600 px-4 py-2 bg-white dark:bg-zinc-800">
                <h3 class="text-lg font-medium text-gray-900 dark:text-zinc-100">Phone number</h3>
                <div v-if="!isEditing">
                    <p class="text-base text-gray-600 dark:text-zinc-300">{{ form.phoneNumber }}</p>
                </div>
                <div v-else>
                    <FormKit type="tel" prefixIcon="phone" v-model="form.phoneNumber" />
                </div>
            </div>

            <div class="w-full border-b border-dotted border-gray-200 dark:border-zinc-600 px-4 py-2 bg-gray-50 dark:bg-zinc-700">
                <h3 class="text-lg font-medium mb-2 text-gray-900 dark:text-zinc-100">Interests</h3>
                <div v-if="!isEditing">
                    <div class="flex flex-wrap gap-2">
                        <button type="button"
                            class="flex items-center bg-gray-100 dark:bg-zinc-600 text-gray-700 dark:text-zinc-200 font-light transition duration-150 rounded-full px-2.5 py-1.5 mb-2 hover:bg-gray-200 dark:hover:bg-zinc-500"
                            v-for="category in form.interests" :key="category.name">
                            <img :src="`${runtimeConfig.public.baseUrl}storage/categories/${category.icon}`"
                                class="w-5 h-5 sm:w-6 sm:h-6 mr-1.5 sm:mr-2" />
                            {{ category.name }}
                        </button>
                    </div>
                </div>
                <div class="mb-3" v-else>
                    <MultiSelect mode="tags" v-model="selectedInterest"
                        :options="$categories.map((category: Category) => category.name)" label="Select interests"
                        :classes="{ container: 'border bg-white dark:bg-zinc-800 dark:border-zinc-600 relative' }" />
                </div>
            </div>
        </div>
    </div>

    <div v-else key="profile-loading" class="animate-pulse">
        <div class="w-full px-4 py-2 border-b border-gray-200 dark:border-zinc-600 flex justify-end">
            <div class="w-28 h-10 bg-gray-200 dark:bg-zinc-600"></div>
        </div>

        <div>
            <div v-for="n in 5" :key="n" class="w-full border-b border-dotted border-gray-200 dark:border-zinc-600 px-4 py-4 bg-gray-50 dark:bg-zinc-700">
                <div class="h-6 bg-gray-200 dark:bg-zinc-600 w-1/3 mb-2"></div>
                <div class="h-8 bg-gray-300 dark:bg-zinc-500 w-full"></div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import type { PropType } from 'vue';
import type { User } from '@/types/user';
import type { GenericResponse } from '@/types/api';
import type { Category } from '@/types';

const props = defineProps({
    profile: {
        type: Object as PropType<User | null | undefined>,
        default: null
    },
    onProfileUpdated: {
        type: Function,
        default: () => {}
    }
});

const emit = defineEmits<{
    profileUpdated: []
}>();

const runtimeConfig = useRuntimeConfig();
const { $toast, $categories }: any = useNuxtApp();
const httpClient = useHttpClient();
const loading = ref<boolean>(false);
const isEditing = ref<boolean>(false);

const form = ref({
    name: "",
    about: "",
    socialMedia: {
        facebook: "",
        twitter: "",
        instagram: "",
    },
    phoneNumber: "",
    interests: [] as Category[]
});

// Initialize form when profile changes
watch(() => props.profile, (newProfile) => {
    if (newProfile) {
        form.value = {
            name: newProfile.name || "",
            about: newProfile.profile?.about || "",
            socialMedia: {
                facebook: newProfile.facebook_url || "",
                twitter: newProfile.twitter_url || "",
                instagram: newProfile.profile?.instagram || "",
            },
            phoneNumber: newProfile.phone || "",
            interests: newProfile.interests || [] as Category[]
        };
    }
}, { immediate: true });

const selectedInterest = ref<string[]>([]);

const toggleEdit = (): void => {
    isEditing.value = !isEditing.value;
};

const updateProfile = async (): Promise<void> => {
    if (!props.profile) return;
    try {
        loading.value = true;

        const getInterestIds: number[] = [];
        $categories.value.forEach((category: Category) => {
            if (selectedInterest.value.includes(category.name)) {
                getInterestIds.push(category.id);
            }
        });

        // Update basic profile fields
        const profileData = {
            name: form.value.name,
            email: props.profile.email,
            facebook_url: form.value.socialMedia.facebook,
            twitter_url: form.value.socialMedia.twitter,
            phone: form.value.phoneNumber,
            interests: getInterestIds
        };

        const profileResponse = await httpClient.post<GenericResponse>(ENDPOINTS.PROFILE.UPDATE, profileData);

        // Update bio separately if it has changed
        if (form.value.about !== props.profile.profile?.about) {
            const bioData = new FormData();
            bioData.append('bio', form.value.about);
            await httpClient.post<GenericResponse>(ENDPOINTS.PROFILE.BIO, bioData);
        }

        if (profileResponse) {
            $toast.success('Profile updated successfully');
            toggleEdit();
            emit('profileUpdated');
            // Refresh the profile data in the parent component
            await props.onProfileUpdated();
        }
    } catch (error: any) {
        if (error.errors) {
            const errors = error.errors;
            Object.keys(errors).forEach((key) => {
                if (Array.isArray(errors[key])) {
                    errors[key].forEach((message: string) => {
                        $toast.error(message);
                    });
                } else if (typeof errors[key] === 'string') {
                    $toast.error(errors[key]);
                }
            });
        } else {
            $toast.error(error.message || 'An error occurred');
        }
    } finally {
        loading.value = false;
    }
}

onMounted(() => {
    if (props.profile) {
        selectedInterest.value = props.profile.interests?.map((interest: Category) => interest.name) || [];
    }
});
</script>
