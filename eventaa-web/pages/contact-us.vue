<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12">
    <div
      class="bg-zinc-50 dark:bg-zinc-900 p-6 sm:p-12 mb-8 sm:mb-12 text-center relative overflow-hidden"
    >
      <div class="absolute inset-0">
        <div class="absolute inset-0">
          <div
            class="absolute left-0 top-0 w-24 sm:w-40 h-24 sm:h-40 rounded-full opacity-20 bg-zinc-200 dark:bg-zinc-700 -translate-x-1/2 -translate-y-1/2"
          ></div>
          <div
            class="absolute right-0 top-0 w-24 sm:w-40 h-24 sm:h-40 rounded-full opacity-20 bg-zinc-200 dark:bg-zinc-700 translate-x-1/2 -translate-y-1/2"
          ></div>
          <div
            class="absolute right-0 bottom-0 w-24 sm:w-40 h-24 sm:h-40 rounded-full opacity-20 bg-zinc-200 dark:bg-zinc-700 translate-x-1/2 translate-y-1/2"
          ></div>
        </div>
      </div>
      <div class="relative">
        <div
          class="inline-block bg-zinc-300 dark:bg-zinc-700 text-zinc-900 dark:text-zinc-100 px-3 py-1 sm:px-4 sm:py-2 rounded-full text-xs sm:text-sm font-medium mb-2 sm:mb-4"
        >
          CONTACT US
        </div>
        <h1 class="text-3xl sm:text-4xl md:text-5xl font-bold text-zinc-900 dark:text-zinc-100">Get In Touch</h1>
        <p class="text-zinc-600 dark:text-zinc-400 mt-4 text-lg">
          Fill out the form below or schedule a meeting with us at your convenience.
        </p>
      </div>
    </div>



    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
      <div>
        <h2 class="text-2xl sm:text-3xl font-bold text-zinc-900 dark:text-zinc-100 mb-4 sm:mb-6">Let's Talk!</h2>
        <p class="text-sm sm:text-base text-zinc-600 dark:text-zinc-400 mb-6 sm:mb-8">
          Get in touch with us using the enquiry form or contact details below.
        </p>

        <!-- Success Message -->
        <div v-if="showSuccess" class="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-800 dark:text-green-200 flex items-center space-x-3">
          <Icon icon="heroicons:check-circle" class="w-5 h-5 text-green-600 dark:text-green-400" />
          <span>{{ successMessage }}</span>
        </div>

        <!-- Error Message -->
        <div v-if="errorMessage" class="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 flex items-center space-x-3">
          <Icon icon="heroicons:exclamation-triangle" class="w-5 h-5 text-red-600 dark:text-red-400" />
          <span>{{ errorMessage }}</span>
        </div>

        <FormKit
          type="form"
          @submit="handleSubmit"
          submit-label=""
          :actions="false"
          class="space-y-4 sm:space-y-6"
          :disabled="loading"
        >
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <FormKit
              type="text"
              name="firstName"
              label="First Name"
              validation="required"
              :disabled="loading"
            />
            <FormKit
              type="text"
              name="lastName"
              label="Last Name"
              validation="required"
              :disabled="loading"
            />
          </div>

          <FormKit
            type="email"
            name="email"
            label="Email"
            validation="required|email"
            prefixIcon="email"
            :disabled="loading"
          />

          <FormKit
            type="textarea"
            name="message"
            label="Message"
            validation="required"
            placeholder="Type something..."
            :rows="4"
            :disabled="loading"
          />

          <Vueform class="mt-2">
            <CheckboxElement
              name="terms_agreement"
              input-class="text-red-600 focus:ring-red-500"
              :disabled="loading"
            >
            By sending this message, you agree to EventaHub Malawi <a href='/privacy-policy' class='text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300'>Privacy Policy</a></CheckboxElement>
          </Vueform>

          <CorePrimaryButton
            :text="loading ? 'Sending...' : 'Send Message'"
            class="w-full sm:w-auto mt-4"
            :loading="loading"
            :disabled="loading"
          />
        </FormKit>
      </div>

      <div class="bg-zinc-50 dark:bg-zinc-900 p-4 sm:p-6 mt-8 lg:mt-0 border border-zinc-200 dark:border-zinc-700">
        <div class="flex justify-center mb-6 sm:mb-8">
          <img
            src="@/assets/illustrations/contact-person.png"
            alt="contact-person-illustration"
            class="h-40 sm:h-56 w-auto object-cover"
          />
        </div>

        <div class="space-y-4 sm:space-y-6">
          <h3 class="text-lg sm:text-xl font-bold text-zinc-900 dark:text-zinc-100 mb-3 sm:mb-4">Quick Contact</h3>

          <div class="flex items-start sm:items-center">
            <div class="flex-shrink-0 bg-red-100 dark:bg-red-900/30 p-2">
              <Icon icon="heroicons:envelope" class="w-5 sm:w-6 h-5 sm:h-6 text-red-600 dark:text-red-400" />
            </div>
            <div class="ml-3 sm:ml-4">
              <p class="text-base sm:text-lg font-semibold text-zinc-900 dark:text-zinc-100">Email:</p>
              <a
                href="mailto:<EMAIL>"
                class="text-sm sm:text-base text-zinc-600 dark:text-zinc-400 hover:text-red-600 dark:hover:text-red-400 transition-colors"
              >
                <EMAIL>
              </a>
            </div>
          </div>

          <div class="flex items-start sm:items-center">
            <div class="flex-shrink-0 bg-red-100 dark:bg-red-900/30 p-2">
              <Icon icon="heroicons:phone" class="w-5 sm:w-6 h-5 sm:h-6 text-red-600 dark:text-red-400" />
            </div>
            <div class="ml-3 sm:ml-4">
              <p class="text-base sm:text-lg font-semibold text-zinc-900 dark:text-zinc-100">Phone Number</p>
              <a
                href="tel:+26588029569"
                class="text-sm sm:text-base text-zinc-600 dark:text-zinc-400 hover:text-red-600 dark:hover:text-red-400 transition-colors"
              >
                Malawi +265 88 0295693
              </a>
            </div>
          </div>

          <div class="flex items-start sm:items-center">
            <div class="flex-shrink-0 bg-red-100 dark:bg-red-900/30 p-2">
              <Icon icon="heroicons:map-pin" class="w-5 sm:w-6 h-5 sm:h-6 text-red-600 dark:text-red-400" />
            </div>
            <div class="ml-3 sm:ml-4">
              <p class="text-base sm:text-lg font-semibold text-zinc-900 dark:text-zinc-100">Headquarters</p>
              <p class="text-sm sm:text-base text-zinc-600 dark:text-zinc-400">
                Area 7, Lilongwe Malawi
              </p>
            </div>
          </div>

          <!-- Business Hours -->
          <div class="flex items-start sm:items-center">
            <div class="flex-shrink-0 bg-red-100 dark:bg-red-900/30 p-2">
              <Icon icon="heroicons:clock" class="w-5 sm:w-6 h-5 sm:h-6 text-red-600 dark:text-red-400" />
            </div>
            <div class="ml-3 sm:ml-4">
              <p class="text-base sm:text-lg font-semibold text-zinc-900 dark:text-zinc-100">Business Hours</p>
              <p class="text-sm sm:text-base text-zinc-600 dark:text-zinc-400">
                Monday - Friday: 8:00 AM - 6:00 PM
              </p>
              <p class="text-sm sm:text-base text-zinc-600 dark:text-zinc-400">
                Saturday: 9:00 AM - 4:00 PM
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useHttpClient } from '@/composables/useHttpClient'
import { ENDPOINTS } from '@/utils/api'

definePageMeta({
  layout: "default",
});

useHead({
  title: "Contact EventaHub Malawi - Get in Touch with Our Team",
  meta: [
    {
      name: "description",
      content: "Get in touch with EventaHub Malawi team. Contact us for support, partnerships, event hosting inquiries, or any questions about our event platform.",
    },
    {
      name: 'keywords',
      content: 'contact EventaHub, support Malawi, event platform support, customer service, get in touch'
    },
    {
      property: 'og:title',
      content: 'Contact EventaHub Malawi - Get in Touch with Our Team'
    },
    {
      property: 'og:description',
      content: 'Get in touch with EventaHub Malawi team. Contact us for support, partnerships, event hosting inquiries, or any questions.'
    },
    {
      property: 'og:type',
      content: 'website'
    },
    {
      name: 'twitter:card',
      content: 'summary_large_image'
    },
    {
      name: 'twitter:title',
      content: 'Contact EventaHub Malawi - Get in Touch with Our Team'
    },
    {
      name: 'twitter:description',
      content: 'Get in touch with EventaHub Malawi team for support, partnerships, or event hosting inquiries.'
    },
    {
      name: 'robots',
      content: 'index, follow'
    }
  ],
});

// Reactive variables
const loading = ref(false)
const showSuccess = ref(false)
const successMessage = ref('')
const errorMessage = ref('')

// Composables
const httpClient = useHttpClient()
const { $toast }: any = useNuxtApp()

const handleSubmit = async (formData: any) => {
  if (loading.value) return

  // Clear previous messages
  showSuccess.value = false
  errorMessage.value = ''

  // Validate terms agreement
  if (!formData.terms_agreement) {
    errorMessage.value = 'Please agree to our Privacy Policy to continue.'
    return
  }

  loading.value = true

  try {
    const response = await httpClient.post<{success: boolean, message: string}>(ENDPOINTS.CONTACT.SUBMIT, {
      firstName: formData.firstName,
      lastName: formData.lastName,
      email: formData.email,
      message: formData.message,
      terms_agreement: formData.terms_agreement
    })

    if (response.success) {
      successMessage.value = response.message || 'Thank you for your message! We will get back to you soon.'
      showSuccess.value = true

      // Show toast notification
      $toast.success(successMessage.value)

      // Reset form after successful submission
      setTimeout(() => {
        showSuccess.value = false
        // You might want to reset the form here if FormKit provides a method
      }, 5000)
    }
  } catch (error: any) {
    console.error('Contact form submission error:', error)

    if (error.response?.data?.errors) {
      // Handle validation errors
      const errors = error.response.data.errors
      const firstError = Object.values(errors)[0] as string[]
      errorMessage.value = firstError[0] || 'Please check your form inputs and try again.'
    } else if (error.response?.data?.message) {
      errorMessage.value = error.response.data.message
    } else {
      errorMessage.value = 'Failed to send your message. Please try again later.'
    }

    $toast.error(errorMessage.value)

    // Hide error message after 8 seconds
    setTimeout(() => {
      errorMessage.value = ''
    }, 8000)
  } finally {
    loading.value = false
  }
};
</script>
