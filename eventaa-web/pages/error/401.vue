<template>
  <div class="min-h-screen bg-white dark:bg-zinc-800 flex items-center justify-center px-4">
    <div class="max-w-lg mx-auto text-center">
      <!-- Logo -->
      <div class="mb-8">
        <img
          src="/icon.png"
          alt="EventaHub Logo"
          class="h-16 w-16 mx-auto rounded-full shadow-lg"
        >
      </div>

      <!-- 401 Illustration -->
      <div class="mb-8">
        <div class="text-9xl font-bold text-blue-600 dark:text-blue-500 mb-4">
          401
        </div>
        <div class="w-32 h-32 mx-auto mb-6">
          <svg class="w-full h-full text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z" clip-rule="evenodd" />
          </svg>
        </div>
      </div>

      <!-- Error Content -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          Authentication Required
        </h1>
        <p class="text-gray-600 dark:text-gray-400 text-lg mb-6">
          You need to be logged in to access this page. Please sign in with your EventaHub account to continue.
        </p>
      </div>

      <!-- Authentication Status -->
      <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6 mb-8">
        <div class="flex items-center justify-center mb-4">
          <svg class="w-6 h-6 text-blue-600 dark:text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z" clip-rule="evenodd" />
          </svg>
          <span class="text-blue-800 dark:text-blue-200 font-medium">Login Required</span>
        </div>
        <p class="text-blue-700 dark:text-blue-300 text-sm">
          Your session may have expired or you need to authenticate.
        </p>
      </div>

      <!-- Sign In Options -->
      <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Sign in to access:
        </h3>
        <ul class="text-left text-gray-600 dark:text-gray-400 space-y-2">
          <li class="flex items-center">
            <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
            Your personal dashboard
          </li>
          <li class="flex items-center">
            <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
            Event booking and management
          </li>
          <li class="flex items-center">
            <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
            Personalized recommendations
          </li>
          <li class="flex items-center">
            <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
            Exclusive member features
          </li>
        </ul>
      </div>

      <!-- Action Buttons -->
      <div class="space-y-4">
        <NuxtLink
          to="/get-started"
          class="inline-block bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg"
        >
          Sign In / Register
        </NuxtLink>

        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <NuxtLink
            to="/"
            class="inline-block bg-gray-600 hover:bg-gray-700 text-white font-semibold py-2 px-6 rounded-lg transition-all duration-200 text-sm shadow-md"
          >
            Back to Home
          </NuxtLink>
          <NuxtLink
            to="/forgot-password"
            class="inline-block bg-gray-600 hover:bg-gray-700 text-white font-semibold py-2 px-6 rounded-lg transition-all duration-200 text-sm shadow-md"
          >
            Forgot Password?
          </NuxtLink>
        </div>
      </div>

      <!-- Additional Help -->
      <div class="mt-8 text-sm text-gray-500 dark:text-gray-400">
        <p>New to EventaHub? <NuxtLink to="/get-started" class="text-blue-600 hover:text-blue-800 underline">Create a free account</NuxtLink></p>
        <p class="mt-1">Having trouble signing in? <NuxtLink to="/contact-us" class="text-blue-600 hover:text-blue-800 underline">Contact support</NuxtLink></p>
      </div>

      <!-- Footer -->
      <div class="mt-12 text-center text-sm text-gray-500 dark:text-gray-400">
        <p>&copy; {{ new Date().getFullYear() }} EventaHub Malawi. All rights reserved.</p>
        <p class="mt-1">Powered by Brainy Technologies</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Set page meta
useHead({
  title: '401 - Authentication Required | EventaHub',
  meta: [
    { name: 'description', content: 'You need to be logged in to access this page. Sign in to your EventaHub account to continue.' },
    { name: 'robots', content: 'noindex, nofollow' }
  ]
})

// Set layout to false for full control
definePageMeta({
  layout: false
})
</script>
