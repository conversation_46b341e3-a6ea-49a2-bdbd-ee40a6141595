<?php

namespace App\Http\Controllers;

use App\Mail\ContactFormMail;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class ContactController extends Controller
{
    /**
     * Handle contact form submission
     */
    public function submit(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'firstName' => 'required|string|max:255',
                'lastName' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'message' => 'required|string|max:2000',
                'terms_agreement' => 'required|accepted'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $contactData = [
                'firstName' => $request->firstName,
                'lastName' => $request->lastName,
                'email' => $request->email,
                'message' => $request->message,
                'submitted_at' => now(),
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent()
            ];

            // Get admin emails from config or use default
            $adminEmails = config('mail.admin_emails', ['<EMAIL>']);

            // Send email to administrators
            foreach ($adminEmails as $adminEmail) {
                Mail::to($adminEmail)->send(new ContactFormMail($contactData));
            }

            Log::info('Contact form submitted successfully', [
                'email' => $request->email,
                'name' => $request->firstName . ' ' . $request->lastName,
                'ip' => $request->ip()
            ]);

            return response()->json([
                'message' => 'Thank you for your message! We will get back to you soon.',
                'success' => true
            ], 200);

        } catch (\Exception $e) {
            Log::error('Contact form submission failed', [
                'error' => $e->getMessage(),
                'email' => $request->email ?? 'unknown',
                'ip' => $request->ip()
            ]);

            return response()->json([
                'message' => 'Failed to send your message. Please try again later.',
                'success' => false
            ], 500);
        }
    }
}
